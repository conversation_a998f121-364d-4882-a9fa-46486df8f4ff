{"name": "judgels-client", "private": true, "dependencies": {"@blueprintjs/core": "5.7.2", "@blueprintjs/datetime": "5.2.8", "@blueprintjs/icons": "5.5.0", "@blueprintjs/select": "5.0.20", "@react-oauth/google": "0.11.1", "ace-builds": "1.32.2", "classnames": "2.5.0", "connected-react-router": "6.9.1", "final-form": "4.20.10", "history": "4.9.0", "html-react-parser": "1.2.6", "katex": "0.13.11", "normalize.css": "5.0.0", "pretty-bytes": "4.0.2", "query-string": "5.1.1", "react": "17.0.2", "react-ace": "10.1.0", "react-app-polyfill": "2.0.0", "react-async-script": "0.9.1", "react-document-title": "2.0.3", "react-dom": "17.0.2", "react-final-form": "6.5.9", "react-ga4": "2.1.0", "react-google-recaptcha": "0.9.9", "react-paginate": "7.1.3", "react-redux": "7.2.4", "react-router": "5.0.1", "react-router-dom": "5.0.1", "react-syntax-highlighter": "8.1.0", "react-transition-group": "2.9.0", "redux": "3.7.2", "redux-persist": "5.10.0", "redux-thunk": "2.4.2", "reset-css": "2.2.1", "tinymce": "4.9.11", "typeface-open-sans": "0.0.54", "typeface-roboto": "0.0.54"}, "scripts": {"ci": "npm-run-all lint test", "start": "vite", "build": "vite build", "preview": "vite preview", "lint": "prettier --list-different 'src/**/*.js' 'src/**/*.jsx' 'src/**/*.scss'", "format": "prettier --write 'src/**/*.js' 'src/**/*.jsx' 'src/**/*.scss'", "test": "TZ=Asia/Jakarta jest --passWithNoTests"}, "devDependencies": {"@babel/core": "7.28.0", "@babel/preset-env": "7.28.0", "@babel/preset-react": "7.27.1", "@trivago/prettier-plugin-sort-imports": "4.3.0", "@vitejs/plugin-react": "4.7.0", "@wojtekmaj/enzyme-adapter-react-17": "0.8.0", "babel-jest": "29.7.0", "enzyme": "3.11.0", "jest": "29.7.0", "jest-environment-jsdom": "29.7.0", "jest-transform-stub": "2.0.0", "jest-watch-typeahead": "2.2.2", "nock": "11.7.2", "npm-run-all": "4.1.5", "prettier": "3.1.1", "redux-mock-store": "1.5.4", "sass": "1.89.2", "vite": "7.0.4", "vite-plugin-static-copy": "3.1.1"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "jest": {"roots": ["<rootDir>/src"], "collectCoverageFrom": ["src/**/*.{js,jsx}"], "setupFiles": ["react-app-polyfill/jsdom"], "setupFilesAfterEnv": ["<rootDir>/src/setupTests.js"], "testMatch": ["<rootDir>/src/**/__tests__/**/*.{js,jsx}", "<rootDir>/src/**/*.{spec,test}.{js,jsx}"], "testEnvironment": "jest-environment-jsdom", "transform": {"^.+\\.(js|jsx)$": "babel-jest", "\\.(css|scss)$": "jest-transform-stub", "\\.(jpg|jpeg|png|gif|eot|otf|webp|svg|ttf|woff|woff2|mp4|webm|wav|mp3|m4a|aac|oga)$": "jest-transform-stub"}, "transformIgnorePatterns": ["[/\\\\]node_modules[/\\\\].+\\.(js|jsx)$", "^.+\\.module\\.(css|sass|scss)$"], "modulePaths": [], "moduleFileExtensions": ["js", "json", "jsx", "node"], "resetMocks": true}}