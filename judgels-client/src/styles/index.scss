@import '../../node_modules/reset-css/reset.css';
@import '../../node_modules/normalize.css/normalize.css';
@import '../../node_modules/@blueprintjs/core/lib/css/blueprint.css';
@import '../../node_modules/@blueprintjs/datetime/lib/css/blueprint-datetime.css';
@import '../../node_modules/@blueprintjs/icons/lib/css/blueprint-icons.css';

@import './base';
@import './table';
@import './ratings';

body {
  font-family: $primary-font;
  background-color: $background-color;

  .bp5-dark {
    background-color: $dark-background-color !important;
  }
}

.bp5-dark {
  &,
  td,
  th,
  table.bp5-html-table td,
  table.bp5-html-table th,
  label,
  .bp5-control,
  .bp5-dialog,
  .bp5-tag.bp5-intent-primary,
  .bp5-breadcrumb-current {
    color: $dark-text-color;
  }
}

h1,
h2,
h3,
h4,
h5 {
  font-family: $accent-font !important;
  font-weight: 500 !important;
  margin: 0 0 10px;
}

small {
  line-height: 14px !important;

  .bp5-light & {
    color: $secondary-text-color;
  }

  .bp5-dark & {
    color: $dark-secondary-text-color !important;
  }
}

b,
strong {
  font-weight: 700;

  .bp5-dark & {
    color: #eeeeee;
  }
}

h2 {
  font-size: 30px !important;
}

h3 {
  font-size: 18px !important;
  line-height: 9px !important;
}

h4 {
  font-size: 16px !important;
  line-height: 20px !important;
}

h5 {
  font-size: 14px !important;
}

@media only screen and (max-width: 750px) {
  h2 {
    font-size: 20px !important;
    line-height: 22px !important;
  }
}

hr {
  margin-top: 15px !important;
  margin-bottom: 15px !important;
  width: 100%;
  border: none;

  .bp5-light & {
    border-bottom: 1px solid $border-color;
  }

  .bp5-dark & {
    border-bottom: 1px solid $dark-border-color;
  }
}

a {
  color: $primary-color;

  .bp5-dark &,
  .bp5-dark &:hover {
    color: #48aff0;
  }
}

ol {
  list-style: decimal;
}

ul {
  list-style: disc;
}

ol,
ul {
  padding-left: 25px;
}

code,
pre,
.ace_editor span {
  font-family: Menlo, Monaco, Consolas, 'Courier New', monospace !important;
}

code,
pre {
  background-color: #f5f5f5;
  border: 1px solid #cccccc;
  border-radius: 4px;

  .bp5-dark & {
    background-color: #293742;
    border-color: #202b33;
  }
}

code {
  font-size: 13px;
  padding: 3px;
}

pre {
  font-size: 13px;
  padding: 9px !important;
  overflow: auto;

  code {
    padding: initial;
    border: initial;
    border-radius: initial;
  }
}

em {
  font-style: oblique;
}

label {
  font-size: $font-size-small;
  font-weight: bold;
}

input {
  font-family: $primary-font;
  width: 100%;
  height: 35px !important;
  border-radius: 2px;
}

select {
  width: 100%;
}

table {
  width: 100%;
}

td,
th {
  .bp5-dark & {
    border-color: $dark-secondary-background-color !important;
  }
}

blockquote {
  border-left: 5px solid #eeeeee;
  padding: 10px 20px;
  margin: 0 0 20px;
}

/* Blueprint overrides */

.toast {
  margin-top: 50px;
}

.bp5-intent-primary {
  &.bp5-button,
  &.bp5-tag {
    background-color: $primary-color;
  }
}

.bp5-intent-warning {
  &.bp5-button,
  &.bp5-tag,
  &.bp5-toast,
  &.bp5-toast .bp5-icon,
  &.bp5-toast .bp5-button,
  &.bp5-toast .bp5-button svg {
    color: #fff !important;
  }

  &.bp5-toast .bp5-button:hover svg {
    background-color: inherit !important;
  }
}

.bp5-intent-warning:not(.bp5-disabled) {
  &:not(:hover).bp5-button,
  &.bp5-tag,
  &.bp5-toast,
  &.bp5-toast .bp5-icon,
  &.bp5-toast .bp5-button,
  &.bp5-toast .bp5-button svg {
    background-color: #d9822b !important;
  }
}

.bp5-button:not([class*='bp5-intent-']) {
  .bp5-dark & {
    color: $dark-text-color;
    background-color: $dark-background-color;
  }

  .bp5-dark &:hover {
    background-color: $dark-tertiary-background-color;
  }

  .bp5-dark &:active {
    background-color: $dark-background-color;
  }

  .bp5-dark &.bp5-active {
    background-color: #202b33;
  }

  .bp5-dark &.bp5-disabled {
    background-color: $dark-background-color;
  }
}

.bp5-callout {
  padding: 10px 12px 9px;

  .bp5-dark &:not([class*='bp5-intent-']) {
    background-color: $dark-tertiary-background-color;
  }

  &.bp5-callout-icon > .bp5-icon:first-child {
    top: 12px;
  }

  &.bp5-intent-primary {
    color: inherit;
    background-color: rgba(19, 124, 189, 0.15);

    .bp5-dark & {
      color: unset;
      background-color: $dark-blue-secondary-background-color;
    }
  }
}

table.bp5-html-table.bp5-html-table-striped tbody tr:nth-child(odd) td {
  background-color: rgba(191, 204, 214, 0.15);

  .bp5-dark & {
    background-color: rgba(92, 112, 128, 0.15);
  }
}

.bp5-card {
  .bp5-dark & {
    background-color: $dark-card-background-color;
    box-shadow:
      0 0 0 1px rgba(16, 22, 26, 0.4),
      0 0 0 rgba(16, 22, 26, 0),
      0 0 0 rgba(16, 22, 26, 0);
  }
}

.bp5-dialog {
  background-color: rgb(235, 241, 245);

  .bp5-dark & {
    background-color: $dark-card-background-color;
  }
}

.bp5-dialog-header {
  .bp5-dark & {
    background-color: $dark-tertiary-background-color;
  }

  .bp5-dark & .bp5-button {
    background-color: $dark-tertiary-background-color;
  }
}

.bp5-menu {
  .bp5-dark & {
    background-color: $dark-background-color;
  }
}

.bp5-menu-item.bp5-active {
  .bp5-dark & {
    color: inherit;
    background-color: $dark-blue-secondary-background-color;
  }
}

.bp5-popover {
  .bp5-dark & {
    box-shadow:
      0 0 0 1px rgba(17, 20, 24, 0.1),
      0 2px 4px rgba(17, 20, 24, 0.2),
      0 8px 24px rgba(17, 20, 24, 0.2);
  }

  .bp5-dark & .bp5-popover-arrow-fill {
    fill: $dark-background-color;
  }

  .bp5-dark & .bp5-popover-arrow-border {
    fill: $dark-background-color;
    fill-opacity: 1;
  }
}

.bp5-file-upload-input::after {
  .bp5-dark & {
    color: $dark-text-color;
    background-color: $dark-background-color;
  }

  .bp5-dark &:hover {
    background-color: $dark-background-color;
  }
}

/* End Blueprint overrides */

.secondary-info {
  font-family: $accent-font;
}

.float-left {
  float: left;
}

.float-right {
  float: right;
}

.multiline-text {
  white-space: pre-wrap;
}

.clearfix::after {
  content: '';
  clear: both;
  display: table;
}

.normal-weight {
  font-weight: normal !important;
}

.heading-with-button-action {
  display: inline-block;
  margin-bottom: 0 !important;
  margin-right: 10px;
  vertical-align: middle;
}

.col-fit {
  width: 1%;
  white-space: nowrap;
}
