.chapter-programming-problem-statement-page {
  flex: 1 1;
  max-width: 865px;

  details summary {
    cursor: pointer;
    margin-bottom: 10px;
  }

  details summary > * {
    display: inline;
  }

  .statement-header {
    display: flex;
    flex-direction: row-reverse;
    flex-wrap: wrap;
    column-gap: 15px;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;

    &__limits {
      line-height: 26px !important;
      margin-bottom: 5px;
      white-space: nowrap;
    }

    &__problem-set-problem-paths {
      flex: 1;
      line-height: 26px !important;
    }
  }

  .chapter-problem-editorial {
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;

    > hr {
      margin-top: 25px !important;
      margin-bottom: 25px !important;
    }
  }
  .chapter-programming-problem-statement-page__navigation {
    text-align: right;
  }
}
