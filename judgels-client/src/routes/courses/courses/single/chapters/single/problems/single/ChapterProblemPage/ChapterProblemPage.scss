@import '../../../../../../../../../styles/base';

.chapter-problem-page {
  flex: 1 1;
  width: 100%;
  overflow-x: auto;
  padding: 0 1px;
}

@media only screen and (min-width: 1025px) {
  .chapter-problem-page {
    height: calc(100vh - 85px);
  }
}

.chapter-problem-page__title {
  display: flex;
  flex-wrap: wrap;
  row-gap: 10px;
  column-gap: 15px;
  margin-bottom: 15px;
}

.chapter-problem-page__title h3 {
  font-size: 22px !important;
  line-height: 24px !important;
  margin-bottom: 0;
}

.chapter-problem-page__title--link {
  color: inherit !important;

  &:hover {
    text-decoration: none;
    color: $primary-color !important;
  }

  & .bp5-icon {
    height: 22px;
  }
}

.chapter-problem-page__title--chevron {
  height: 24px;
  color: $dark-secondary-background-color !important;
}

.chapter-problem-page__prev {
  margin-right: 5px;
}
