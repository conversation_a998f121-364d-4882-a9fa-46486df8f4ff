.chapter-problem-card {
  display: flex;
  flex-direction: column;
  row-gap: 5px;

  .chapter-problem-card__heading {
    display: flex;
    gap: 10px;
  }

  h4 {
    flex: 1 1;
    margin-bottom: 0;
  }

  .bp5-icon {
    padding-top: 1px;
  }

  .bp5-tag {
    height: 21px;
  }

  &__problem-set-problem-paths {
    font-weight: 600;
  }

  &--future {
    background-color: inherit;

    > *,
    .chapter-problem-card__heading > .bp5-icon {
      font-weight: normal !important;
      color: #bcc0c2;

      .bp5-dark & {
        color: #5d6c79;
      }

      a:hover & {
        color: inherit !important;
        font-weight: inherit !important;
      }
    }

    .bp5-tag {
      background-color: #ced4d8;

      .bp5-dark & {
        background-color: #5f6d79;
      }
    }
  }
}
