@import '../../../../../../../../../styles/base';

.chapter-lesson-page {
  flex: 1 1;
  max-width: 865px;
}

.chapter-lesson-page__title {
  display: flex;
  flex-wrap: wrap;
  row-gap: 10px;
  column-gap: 15px;
  margin-bottom: 15px;
}

.chapter-lesson-page__title h3 {
  font-size: 22px !important;
  line-height: 24px !important;
  margin-bottom: 0;
}

.chapter-lesson-page__title--link {
  color: inherit !important;

  &:hover {
    text-decoration: none;
    color: $primary-color !important;
  }
}

.chapter-lesson-page__title--chevron {
  height: 24px;
  color: $dark-secondary-background-color !important;
}

.chapter-lesson-page__prev {
  margin-right: 5px;
}
