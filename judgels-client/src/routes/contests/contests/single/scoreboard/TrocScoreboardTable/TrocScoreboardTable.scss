@import '../../../../../../styles/base';

table.troc-scoreboard__content {
  td {
    span.top {
      font-size: 12px;
    }
    span.total-points-cell {
      font-size: 14px !important;
      line-height: 14px;
    }
    span.bottom {
      color: $secondary-text-color;
    }
  }

  td.first-accepted {
    span.top,
    span.bottom {
      color: #fff;
    }
    background: #1f8763 !important;
  }

  td.accepted {
    background: #e1fdd7 !important;
  }

  td.not-accepted {
    span.top,
    span.bottom {
      color: #fff;
    }
    background: #e36262 !important;
  }

  td.frozen {
    span.top,
    span.bottom {
      color: #fff;
    }
    background: #70a2ff !important;
  }

  .clickable {
    cursor: pointer;
  }
}

.bp5-dark table.troc-scoreboard__content {
  td {
    span.bottom {
      color: $dark-secondary-text-color !important;
    }
  }

  td.first-accepted,
  td.not-accepted,
  td.frozen {
    span.bottom {
      color: inherit !important;
    }
  }

  td.accepted {
    background: #e1fdd7 !important;
    color: $dark-background-color !important;

    span.bottom {
      color: $gray2 !important;
    }
  }
}
