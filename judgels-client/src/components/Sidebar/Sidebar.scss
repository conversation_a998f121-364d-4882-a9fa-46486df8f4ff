@import '../../styles/base';

.card-sidebar {
  *:focus {
    outline: none;
  }

  .bp5-icon {
    padding-bottom: 1px;
  }

  .card__content {
    padding-top: 0;
    padding-bottom: 0;
  }

  .bp5-tab-list {
    left: -20px;
    width: 270px;
  }

  .bp5-tab-indicator {
    border-radius: 0 !important;

    .bp5-light & {
      background-color: $secondary-background-color !important;
      border-left: 3px solid $primary-color;
    }

    .bp5-dark & {
      background-color: $dark-secondary-background-color !important;
      border-left: 3px solid $gray3;
    }
  }

  .bp5-tab {
    position: relative;
    padding-left: 20px !important;
    line-height: 50px;
    height: 50px;

    &:hover {
      .bp5-light & {
        background-color: $tertiary-background-color !important;
      }

      .bp5-dark & {
        background-color: $dark-tertiary-background-color !important;
      }
    }

    &[aria-selected='true'] {
      font-weight: bold;

      .bp5-light & {
        color: $text-color;
      }

      .bp5-dark & {
        color: inherit;
      }
    }

    &[aria-selected='false'] {
      .bp5-light & {
        color: $primary-color;
      }
    }

    a {
      width: 100%;

      .bp5-dark & {
        color: $dark-text-color !important;
      }
    }
  }
}

.card-sidebar__arrow {
  float: right;

  .bp5-light & {
    color: $primary-color !important;
  }
}

.card-sidebar__full {
  display: block;
}

.card-sidebar__responsive {
  display: none !important;
}

@media only screen and (max-width: 750px) {
  .card-sidebar__full {
    display: none !important;
  }

  .card-sidebar__responsive {
    display: block !important;
    width: 200px;
    top: -15px;

    .bp5-tab-list {
      left: 0;
    }
  }

  .card-sidebar__responsive-menu {
    .bp5-icon {
      padding-bottom: 0 !important;
    }
  }
}
